"use client";
import { useSignal } from "@preact-signals/safe-react";
import { Player } from "@remotion/player";
import ImageWatermark from "@repo/core/assets/images/watermark.png";
import { cn } from "@repo/ui/lib/utils";
import dynamic from "next/dynamic";
import Image from "next/image";
import { ComponentType, useCallback } from "react";
import { useDoubleTap } from "use-double-tap";
import { LongPressEventType, useLongPress } from "use-long-press";

const VolcengineVideo = dynamic(
  () => import("@repo/core/components/volcengine-video/volcengine-video"),
  {
    ssr: false,
  }
);

export function GuidePlayer<T extends Record<string, unknown>>({
  className,
  component,
  inputProps,
  userId,
  width,
  height,
}: {
  className?: string;
  component: ComponentType<T>;
  inputProps: T;
  userId?: number | string;
  width: number;
  height: number;
}) {
  const {
    playRate,
    refPlayer,
    refVolcenginePlayer,
    data,
    togglePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    progress,
    togglePlay,
    trackEventWithLessonId,
    active,
    durationInFrames,
  } = useGuideViewContext();

  const isComment = useSignal(false);

  const longPressHandlers = useLongPress(
    (e) => {
      // todo)): 这里传的太麻烦，我先用dom直接弄了
      const [...doms] = document.querySelectorAll("[data-name=line-container]");
      if (doms.some((dom) => dom.contains(e.target as HTMLElement))) {
        isComment.value = true;
        return;
      }
      set3XPlayRate();
    },
    {
      onFinish: () => {
        if (isComment.value) {
          isComment.value = false;
          return;
        }
        resetPlayRate();
        trackEventWithLessonId("doc_fast_forward_longpress");
      },
      detect: LongPressEventType.Touch,
    }
  );

  const handleClick = useCallback(() => {
    togglePlayerControls();
  }, [togglePlayerControls]);

  const handleDoubleClick = useCallback(() => {
    togglePlay();
    trackEventWithLessonId("doc_play_pause_doubleclick");
  }, [togglePlay, trackEventWithLessonId]);

  const doubleTapHandlers = useDoubleTap(handleDoubleClick, 300, {
    onSingleTap: handleClick,
  });

  if (!data) {
    return <div>无数据</div>;
  }
  const { avatar } = data;

  return (
    <div
      {...doubleTapHandlers}
      data-name="guide-player"
      className="relative h-screen w-full"
      {...longPressHandlers()}
    >
      <Player
        ref={refPlayer}
        className={cn("h-full w-full", className)}
        style={{ height }}
        component={component}
        inputProps={inputProps}
        initialFrame={progress.frame}
        durationInFrames={durationInFrames}
        fps={avatar.fps}
        playbackRate={playRate.value}
        allowFullscreen={false}
        compositionWidth={width}
        compositionHeight={height}
        acknowledgeRemotionLicense
        errorFallback={(e: { error: { message: string } }) => (
          <span className="text-sm text-red-500">错误: {e.error.message}</span>
        )}
      />
      {active && (
        <>
          <Image
            src={ImageWatermark}
            alt="watermark"
            className="z-52 right-4.5 absolute bottom-3 w-[56px] bg-transparent"
            unoptimized
          />
          <div className="max-w-1/5 pointer-events-none absolute bottom-0 right-0 z-50 w-[calc(100%-var(--width-guide))]">
            <VolcengineVideo
              ref={refVolcenginePlayer}
              className="relative flex h-full w-full flex-col items-center justify-end"
              src={avatar.url}
              startTime={progress.frame / avatar.fps}
              playRate={playRate.value}
              userId={userId}
              tag="文稿组件"
            />
          </div>
        </>
      )}
    </div>
  );
}
