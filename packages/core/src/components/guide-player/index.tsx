"use client";
import { Player } from "@remotion/player";
import ImageWatermark from "@repo/core/assets/images/watermark.png";
import { cn } from "@repo/ui/lib/utils";
import dynamic from "next/dynamic";
import Image from "next/image";
import { ComponentType } from "react";
import {
  useGuidePlayer,
  UseGuidePlayerProps,
} from "../../hooks/use-guide-player";

const VolcengineVideo = dynamic(
  () => import("@repo/core/components/volcengine-video/volcengine-video"),
  {
    ssr: false,
  }
);

export function GuidePlayer<T extends Record<string, unknown>>({
  className,
  component,
  inputProps,
  userId,
  width,
  height,
  ...guidePlayerProps
}: {
  className?: string;
  component: ComponentType<T>;
  inputProps: T;
  userId?: number | string;
  width: number;
  height: number;
} & UseGuidePlayerProps) {
  const {
    playRate,
    refPlayer,
    refVolcenginePlayer,
    data,
    progress,
    active,
    durationInFrames,
    longPressHandlers,
    doubleTapHandlers,
  } = useGuidePlayer(guidePlayerProps);

  if (!data || !data.avatar) {
    return <div>无数据</div>;
  }
  const { avatar } = data;

  return (
    <div
      {...doubleTapHandlers}
      data-name="guide-player"
      className="relative h-screen w-full"
      {...longPressHandlers()}
    >
      <Player
        ref={refPlayer}
        className={cn("h-full w-full", className)}
        style={{ height }}
        component={component}
        inputProps={inputProps}
        initialFrame={progress.frame}
        durationInFrames={durationInFrames}
        fps={avatar.fps}
        playbackRate={playRate.value}
        allowFullscreen={false}
        compositionWidth={width}
        compositionHeight={height}
        acknowledgeRemotionLicense
        errorFallback={(e: { error: { message: string } }) => (
          <span className="text-sm text-red-500">错误: {e.error.message}</span>
        )}
      />
      {active && (
        <>
          <Image
            src={ImageWatermark}
            alt="watermark"
            className="z-52 right-4.5 absolute bottom-3 w-[56px] bg-transparent"
            unoptimized
          />
          <div className="max-w-1/5 pointer-events-none absolute bottom-0 right-0 z-50 w-[calc(100%-var(--width-guide))]">
            <VolcengineVideo
              ref={refVolcenginePlayer}
              className="relative flex h-full w-full flex-col items-center justify-end"
              src={avatar.url}
              startTime={progress.frame / avatar.fps}
              playRate={playRate.value}
              userId={userId}
              tag="文稿组件"
            />
          </div>
        </>
      )}
    </div>
  );
}
