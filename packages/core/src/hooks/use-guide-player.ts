import { Signal, useSignal } from "@preact-signals/safe-react";
import { PlayerRef } from "@remotion/player";
import { VolcenginePlayer } from "@repo/core/components/volcengine-video/volcengine-video";
import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import { RefObject, useCallback } from "react";
import { useDoubleTap } from "use-double-tap";
import { LongPressEventType, useLongPress } from "use-long-press";

// 定义进度类型，兼容不同应用的进度结构
export interface GuideProgress {
  frame: number;
}

export interface UseGuidePlayerProps {
  playRate: Signal<number>;
  refPlayer: RefObject<PlayerRef | null>;
  refVolcenginePlayer: Signal<VolcenginePlayer | null>;
  data: GuideWidgetData;
  togglePlayerControls: () => void;
  set3XPlayRate: () => void;
  resetPlayRate: () => void;
  progress: GuideProgress;
  togglePlay: () => void;
  trackEventWithLessonId: (eventID: string, needWidgetInfo?: boolean) => void;
  active: boolean;
  durationInFrames: number;
}

export interface UseGuidePlayerReturn {
  playRate: Signal<number>;
  refPlayer: RefObject<PlayerRef | null>;
  refVolcenginePlayer: Signal<VolcenginePlayer | null>;
  data: GuideWidgetData;
  togglePlayerControls: () => void;
  set3XPlayRate: () => void;
  resetPlayRate: () => void;
  progress: GuideProgress;
  togglePlay: () => void;
  trackEventWithLessonId: (eventID: string, needWidgetInfo?: boolean) => void;
  active: boolean;
  durationInFrames: number;
  // 新增的交互处理方法
  isComment: Signal<boolean>;
  longPressHandlers: ReturnType<typeof useLongPress>;
  handleClick: () => void;
  handleDoubleClick: () => void;
  doubleTapHandlers: ReturnType<typeof useDoubleTap>;
}

/**
 * 可复用的Guide播放器hook
 * 提供播放器的基础功能和交互处理
 */
export function useGuidePlayer(
  props: UseGuidePlayerProps
): UseGuidePlayerReturn {
  const {
    playRate,
    refPlayer,
    refVolcenginePlayer,
    data,
    togglePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    progress,
    togglePlay,
    trackEventWithLessonId,
    active,
    durationInFrames,
  } = props;

  const isComment = useSignal(false);

  const longPressHandlers = useLongPress(
    (e) => {
      // todo)): 这里传的太麻烦，我先用dom直接弄了
      const [...doms] = document.querySelectorAll("[data-name=line-container]");
      if (doms.some((dom) => dom.contains(e.target as HTMLElement))) {
        isComment.value = true;
        return;
      }
      set3XPlayRate();
    },
    {
      onFinish: () => {
        if (isComment.value) {
          isComment.value = false;
          return;
        }
        resetPlayRate();
        trackEventWithLessonId("doc_fast_forward_longpress");
      },
      detect: LongPressEventType.Touch,
    }
  );

  const handleClick = useCallback(() => {
    togglePlayerControls();
  }, [togglePlayerControls]);

  const handleDoubleClick = useCallback(() => {
    togglePlay();
    trackEventWithLessonId("doc_play_pause_doubleclick");
  }, [togglePlay, trackEventWithLessonId]);

  const doubleTapHandlers = useDoubleTap(handleDoubleClick, 300, {
    onSingleTap: handleClick,
  });

  return {
    // 传入的原始属性
    playRate,
    refPlayer,
    refVolcenginePlayer,
    data,
    togglePlayerControls,
    set3XPlayRate,
    resetPlayRate,
    progress,
    togglePlay,
    trackEventWithLessonId,
    active,
    durationInFrames,
    // 新增的交互处理
    isComment,
    longPressHandlers,
    handleClick,
    handleDoubleClick,
    doubleTapHandlers,
  };
}
