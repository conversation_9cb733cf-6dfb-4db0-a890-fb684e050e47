# useGuidePlayer Hook

一个可复用的Guide播放器hook，提供播放器的基础功能和交互处理。

## 功能特性

- 播放器基础控制（播放/暂停、倍速播放等）
- 长按手势处理（3倍速播放）
- 双击手势处理（播放/暂停）
- 单击手势处理（显示/隐藏控制器）
- 评论区域检测

## 使用方法

### 基本用法

```typescript
import { useGuidePlayer } from "@repo/core/hooks/use-guide-player";

function MyGuidePlayer() {
  // 从你的context或props中获取这些值
  const contextValues = useYourGuideContext();
  
  const {
    longPressHandlers,
    doubleTapHandlers,
    // 其他返回值...
  } = useGuidePlayer({
    playRate: contextValues.playRate,
    refPlayer: contextValues.refPlayer,
    refVolcenginePlayer: contextValues.refVolcenginePlayer,
    data: contextValues.data,
    togglePlayerControls: contextValues.togglePlayerControls,
    set3XPlayRate: contextValues.set3XPlayRate,
    resetPlayRate: contextValues.resetPlayRate,
    progress: contextValues.progress,
    togglePlay: contextValues.togglePlay,
    trackEventWithLessonId: contextValues.trackEventWithLessonId,
    active: contextValues.active,
    durationInFrames: contextValues.durationInFrames,
  });

  return (
    <div
      {...doubleTapHandlers}
      {...longPressHandlers()}
      className="guide-player-container"
    >
      {/* 你的播放器内容 */}
    </div>
  );
}
```

### 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| `playRate` | `Signal<number>` | 播放速率信号 |
| `refPlayer` | `RefObject<PlayerRef \| null>` | Remotion播放器引用 |
| `refVolcenginePlayer` | `Signal<VolcenginePlayer \| null>` | 火山引擎播放器引用 |
| `data` | `GuideWidgetData` | 引导数据 |
| `togglePlayerControls` | `() => void` | 切换播放器控制器显示状态 |
| `set3XPlayRate` | `() => void` | 设置3倍速播放 |
| `resetPlayRate` | `() => void` | 重置播放速率 |
| `progress` | `GuideProgress` | 播放进度 |
| `togglePlay` | `() => void` | 切换播放/暂停状态 |
| `trackEventWithLessonId` | `(eventID: string, needWidgetInfo?: boolean) => void` | 事件追踪函数 |
| `active` | `boolean` | 是否激活状态 |
| `durationInFrames` | `number` | 总帧数 |

### 返回值说明

| 返回值 | 类型 | 说明 |
|--------|------|------|
| `longPressHandlers` | `ReturnType<typeof useLongPress>` | 长按手势处理器 |
| `doubleTapHandlers` | `ReturnType<typeof useDoubleTap>` | 双击手势处理器 |
| `isComment` | `Signal<boolean>` | 是否在评论区域 |
| `handleClick` | `() => void` | 单击处理函数 |
| `handleDoubleClick` | `() => void` | 双击处理函数 |
| ...其他传入的参数 | - | 原样返回传入的参数 |

## 交互行为

### 长按手势
- 在非评论区域长按：触发3倍速播放
- 在评论区域长按：不触发3倍速播放
- 长按结束：恢复正常播放速率并记录事件

### 双击手势
- 双击：切换播放/暂停状态并记录事件

### 单击手势
- 单击：切换播放器控制器显示状态

## 注意事项

1. 确保传入的所有参数都是有效的
2. `data`参数必须包含有效的`avatar`属性
3. 手势处理依赖于DOM结构中的`[data-name=line-container]`元素来检测评论区域
4. 该hook依赖于`use-double-tap`和`use-long-press`库
